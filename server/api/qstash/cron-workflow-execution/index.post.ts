import { firestore } from '~/helpers/firebase'

// Workflow type definition
interface Workflow {
  id: string
  name: string
  description: string
  type: string
  trigger: string
  triggerTiming: { time: string, when: string }
  active: boolean
  sendEmail: boolean
  sendSms: boolean
  sendOta: boolean
  steps: any[]
  listingIds: string[]
  userId: string
  createdAt: number
}

// Function to convert duration string to milliseconds
function durationToMilliseconds(duration: string): number {
  const match = duration.match(/^(\d+)([sdh])$/)
  if (!match) {
    throw new Error(`Invalid duration format: ${duration}`)
  }

  const value = parseInt(match[1])
  const unit = match[2]

  switch (unit) {
    case 's': // seconds
      return value * 1000
    case 'h': // hours
      return value * 60 * 60 * 1000
    case 'd': // days
      return value * 24 * 60 * 60 * 1000
    default:
      throw new Error(`Unsupported time unit: ${unit}`)
  }
}

// Function to generate timestamp based on triggerTiming
function generateTriggerTimestamp(triggerTiming: { time: string, when: string }, baseTimestamp: number): number {
  const durationMs = durationToMilliseconds(triggerTiming.time)

  if (triggerTiming.when === 'before') {
    return baseTimestamp - durationMs
  } else if (triggerTiming.when === 'after') {
    return baseTimestamp + durationMs
  } else {
    throw new Error(`Unsupported timing: ${triggerTiming.when}`)
  }
}

export default defineEventHandler(async (event) => {

  const midnightUTC = new Date(Date.now());
  midnightUTC.setUTCHours(0, 0, 0, 0);
  const midnightTimestamp = midnightUTC.getTime();

  console.log('midnight timestamp: ', midnightTimestamp)

  const workflowsSnap = await firestore.collection('workflows').where('active', '==', true).where('type', '==', 'upsell').get()
  const workflows: any[] = workflowsSnap.docs.map(d => ({ ...d.data(), id: d.id } as Workflow))

  console.log('workflows: ', workflows)

  for (const workflow of workflows) {
    // Generate timestamp for when to send the message
    // For now using midnightTimestamp as base - you'll replace this with actual check-in/check-out times
    const triggerTimestamp = generateTriggerTimestamp(
      workflow.triggerTiming,
      midnightTimestamp // Replace with actual check-in/check-out timestamp
    )

    console.log(`Workflow ${workflow.name}: trigger at ${new Date(triggerTimestamp).toISOString()} (${triggerTimestamp}ms)`)
  }

  return 'Hello Nitro'
})
